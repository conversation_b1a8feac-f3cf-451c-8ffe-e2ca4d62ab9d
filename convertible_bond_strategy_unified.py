#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
可转债量化策略 - 统一数据处理版本
实现数据预处理、统一数据集创建和优化筛选逻辑
"""

import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path
import pickle
import warnings
from typing import Dict, List, Tuple
from get_db_funs import get_db_data

warnings.filterwarnings('ignore')


class UnifiedDataProcessor:
    """统一数据处理器 - 负责数据预处理和统一数据集创建"""
    
    def __init__(self, start_date: str, end_date: str, data_folder: Path):
        self.start_date = start_date
        self.end_date = end_date
        self.data_folder = data_folder
        self.unified_dataset = pd.DataFrame()
        
    def get_data_file_path(self, data_type: str) -> Path:
        """获取数据文件路径"""
        return self.data_folder / f"{data_type}_{self.start_date}_{self.end_date}.pkl"
    
    def save_data(self, data: pd.DataFrame, data_type: str):
        """保存数据到本地"""
        file_path = self.get_data_file_path(data_type)
        with open(file_path, 'wb') as f:
            pickle.dump(data, f)
        print(f"{data_type}数据已保存到: {file_path}")
    
    def load_data(self, data_type: str) -> pd.DataFrame:
        """从本地加载数据"""
        file_path = self.get_data_file_path(data_type)
        if file_path.exists():
            with open(file_path, 'rb') as f:
                data = pickle.load(f)
            print(f"从本地加载{data_type}数据: {len(data)}条")
            return data
        else:
            print(f"本地不存在{data_type}数据文件")
            return pd.DataFrame()
    
    def fetch_all_raw_data(self):
        """获取所有原始数据"""
        print("=" * 80)
        print("开始获取所有原始数据...")
        print("=" * 80)
        
        # 1. 获取交易日历
        trading_dates = self._fetch_trading_dates()
        
        # 2. 获取可转债基础信息
        bond_info = self._fetch_convertible_bonds()

        
        bond_codes = bond_info['S_INFO_WINDCODE'].tolist()
        print(f"将获取 {len(bond_codes)} 只可转债的相关数据")
        
        # 3. 获取可转债相关数据
        self._fetch_bond_valuation_data(bond_codes)
        self._fetch_bond_balance_data(bond_codes)
        self._fetch_bond_rating_data(bond_codes)
        self._fetch_bond_price_data(bond_codes)
        
        # 4. 获取可转债-正股映射关系
        mapping_data = self._fetch_bond_stock_mapping(bond_codes)
        
        if not mapping_data.empty:
            stock_codes = mapping_data['S_INFO_UNDERLYINGWINDCODE'].unique().tolist()
            print(f"将获取 {len(stock_codes)} 只正股的相关数据")
            
            # 5. 获取正股相关数据
            self._fetch_stock_price_data(stock_codes)
            self._fetch_stock_market_cap_data(stock_codes)
            self._fetch_stock_financial_data(stock_codes)
        
        print("=" * 80)
        print("所有原始数据获取完成！")
        print("=" * 80)
        return True
    
    def _fetch_trading_dates(self):
        """获取交易日历"""
        print("获取交易日历数据...")
        
        local_data = self.load_data('trading_dates')
        if not local_data.empty:
            return local_data
        
        try:
            start_date_str = self.start_date.replace('-', '')
            end_date_str = self.end_date.replace('-', '')
            
            conditions = {
                'S_INFO_EXCHMARKET': 'SSE',
                'TRADE_DAYS': [start_date_str, end_date_str]
            }
            
            trade_data = get_db_data('AShareCalendar', keywords=['TRADE_DAYS'], additional_conditions=conditions)
            
            if trade_data is not None and not trade_data.empty:
                trade_data = trade_data.rename(columns={'TRADE_DAYS': 'TRADE_DT'})
                trade_data = trade_data.sort_values('TRADE_DT').reset_index(drop=True)
                
                self.save_data(trade_data, 'trading_dates')
                print(f"获取到 {len(trade_data)} 个交易日")
                return trade_data
            else:
                print("获取交易日历失败")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"获取交易日历出错: {e}")
            return pd.DataFrame()
    
    def _fetch_convertible_bonds(self):
        """获取可转债基础信息"""
        print("获取可转债基础信息...")
        
        local_data = self.load_data('convertible_bonds')
        if not local_data.empty:
            return local_data
        
        try:
            keywords = ['S_INFO_WINDCODE', 'S_INFO_NAME', 'B_INFO_SPECIALBONDTYPE']
            conditions = {
                'OR': [
                    {'B_INFO_SPECIALBONDTYPE': ('LIKE', '%可转债%')},
                    {'B_INFO_SPECIALBONDTYPE': ('LIKE', '%可转换%')},
                    {'B_INFO_SPECIALBONDTYPE': ('LIKE', '%转债%')}
                ]
            }
            
            bond_data = get_db_data('CBondDescription', keywords=keywords, additional_conditions=conditions)
            
            if bond_data is not None and not bond_data.empty:
                pattern = r'\.(SZ|SH)$'
                mask = bond_data['S_INFO_WINDCODE'].str.contains(pattern, regex=True, na=False)
                bond_data = bond_data[mask]
                
                self.save_data(bond_data, 'convertible_bonds')
                print(f"获取到 {len(bond_data)} 只可转债基础信息")
                return bond_data
            else:
                print("获取可转债基础信息失败")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"获取可转债基础信息出错: {e}")
            return pd.DataFrame()

    def _fetch_bond_valuation_data(self, bond_codes: List[str]):
        """获取可转债估值数据"""
        print("获取可转债估值数据...")

        local_data = self.load_data('bond_valuation')
        if not local_data.empty:
            return local_data

        try:
            keywords = ['S_INFO_WINDCODE', 'TRADE_DT', 'CB_ANAL_YTM', 'CB_ANAL_PTM', 'CB_ANAL_CONVPREMIUMRATIO']
            conditions = {
                'S_INFO_WINDCODE': bond_codes,
                'TRADE_DT': [self.start_date, self.end_date]
            }

            valuation_data = get_db_data('CCBondValuation', keywords=keywords, additional_conditions=conditions)

            if valuation_data is not None and not valuation_data.empty:
                numeric_cols = ['CB_ANAL_YTM', 'CB_ANAL_PTM', 'CB_ANAL_CONVPREMIUMRATIO']
                for col in numeric_cols:
                    if col in valuation_data.columns:
                        valuation_data[col] = pd.to_numeric(valuation_data[col], errors='coerce')

                self.save_data(valuation_data, 'bond_valuation')
                print(f"获取到 {len(valuation_data)} 条可转债估值数据")
                return valuation_data
            else:
                print("获取可转债估值数据失败")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取可转债估值数据出错: {e}")
            return pd.DataFrame()

    def _fetch_bond_balance_data(self, bond_codes: List[str]):
        """获取可转债余额数据"""
        print("获取可转债余额数据...")

        local_data = self.load_data('bond_balance')
        if not local_data.empty:
            return local_data

        try:
            keywords = ['S_INFO_WINDCODE', 'S_INFO_CHANGEDATE', 'B_INFO_OUTSTANDINGBALANCE']
            conditions = {
                'S_INFO_WINDCODE': bond_codes,
                'S_INFO_CHANGEDATE': [self.start_date.replace('-', ''), self.end_date.replace('-', '')]
            }

            balance_data = get_db_data('CCBondAmount', keywords=keywords, additional_conditions=conditions)

            if balance_data is not None and not balance_data.empty:
                balance_data['B_INFO_OUTSTANDINGBALANCE'] = pd.to_numeric(
                    balance_data['B_INFO_OUTSTANDINGBALANCE'], errors='coerce'
                ) * 10000  # 万元转元

                self.save_data(balance_data, 'bond_balance')
                print(f"获取到 {len(balance_data)} 条可转债余额数据")
                return balance_data
            else:
                print("获取可转债余额数据失败")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取可转债余额数据出错: {e}")
            return pd.DataFrame()

    def _fetch_bond_rating_data(self, bond_codes: List[str]):
        """获取可转债评级数据"""
        print("获取可转债评级数据...")

        local_data = self.load_data('bond_rating')
        if not local_data.empty:
            return local_data

        try:
            keywords = ['S_INFO_WINDCODE', 'ANN_DT', 'B_INFO_CREDITRATING']
            conditions = {
                'S_INFO_WINDCODE': bond_codes,
                'ANN_DT': [self.start_date, self.end_date]
            }

            rating_data = get_db_data('CBondRating', keywords=keywords, additional_conditions=conditions)

            if rating_data is not None and not rating_data.empty:
                self.save_data(rating_data, 'bond_rating')
                print(f"获取到 {len(rating_data)} 条可转债评级数据")
                return rating_data
            else:
                print("获取可转债评级数据失败")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取可转债评级数据出错: {e}")
            return pd.DataFrame()

    def _fetch_bond_price_data(self, bond_codes: List[str]):
        """获取可转债价格和成交额数据"""
        print("获取可转债价格和成交额数据...")

        local_data = self.load_data('bond_prices')
        if not local_data.empty:
            return local_data

        try:
            keywords = ['S_INFO_WINDCODE', 'TRADE_DT', 'S_DQ_CLOSE', 'S_DQ_AMOUNT']
            conditions = {
                'S_INFO_WINDCODE': bond_codes,
                'TRADE_DT': [self.start_date, self.end_date]
            }

            price_data = get_db_data('CBondEODPrices', keywords=keywords, additional_conditions=conditions)

            if price_data is not None and not price_data.empty:
                # 数据类型转换
                price_data['S_DQ_CLOSE'] = pd.to_numeric(price_data['S_DQ_CLOSE'], errors='coerce')
                price_data['S_DQ_AMOUNT'] = pd.to_numeric(price_data['S_DQ_AMOUNT'], errors='coerce') * 1000  # 千元转元

                self.save_data(price_data, 'bond_prices')
                print(f"获取到 {len(price_data)} 条可转债价格和成交额数据")
                return price_data
            else:
                print("获取可转债价格和成交额数据失败")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取可转债价格和成交额数据出错: {e}")
            return pd.DataFrame()

    def _fetch_bond_stock_mapping(self, bond_codes: List[str]):
        """获取可转债与正股的映射关系"""
        print("获取可转债-正股映射关系...")

        local_data = self.load_data('bond_stock_mapping')
        if not local_data.empty:
            return local_data

        try:
            # 第一步：获取可转债到公司代码的映射
            mapping_keywords = ['S_INFO_WINDCODE', 'S_INFO_COMPCODE']
            mapping_conditions = {'S_INFO_WINDCODE': bond_codes}

            mapping_data = get_db_data('CCBondIssuance', keywords=mapping_keywords, additional_conditions=mapping_conditions)

            if mapping_data is not None and not mapping_data.empty:
                # 第二步：获取公司代码到股票代码的映射
                comp_keywords = ['S_INFO_COMPCODE', 'S_INFO_WINDCODE']
                comp_conditions = {'S_INFO_COMPCODE': mapping_data['S_INFO_COMPCODE'].tolist()}

                comp_data = get_db_data('AShareDescription', keywords=comp_keywords, additional_conditions=comp_conditions)

                if comp_data is not None and not comp_data.empty:
                    comp_to_stock = dict(zip(comp_data['S_INFO_COMPCODE'], comp_data['S_INFO_WINDCODE']))

                    final_mapping = []
                    for _, row in mapping_data.iterrows():
                        bond_code = row['S_INFO_WINDCODE']
                        comp_code = row['S_INFO_COMPCODE']
                        if comp_code in comp_to_stock:
                            stock_code = comp_to_stock[comp_code]
                            final_mapping.append({
                                'S_INFO_WINDCODE': bond_code,
                                'S_INFO_UNDERLYINGWINDCODE': stock_code
                            })

                    if final_mapping:
                        final_data = pd.DataFrame(final_mapping)
                        self.save_data(final_data, 'bond_stock_mapping')
                        print(f"获取到 {len(final_data)} 个可转债-正股映射关系")
                        return final_data
                    else:
                        print("没有找到有效的映射关系")
                        return pd.DataFrame()
                else:
                    print("获取公司代码到股票代码映射失败")
                    return pd.DataFrame()
            else:
                print("获取可转债到公司代码映射失败")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取可转债-正股映射关系出错: {e}")
            return pd.DataFrame()

    def _fetch_stock_price_data(self, stock_codes: List[str]):
        """获取正股价格数据"""
        print("获取正股价格数据...")

        local_data = self.load_data('stock_prices')
        if not local_data.empty:
            return local_data

        try:
            keywords = ['S_INFO_WINDCODE', 'TRADE_DT', 'S_DQ_CLOSE', 'S_DQ_AMOUNT']
            conditions = {
                'S_INFO_WINDCODE': stock_codes,
                'TRADE_DT': [self.start_date, self.end_date]
            }

            price_data = get_db_data('AShareEODPrices', keywords=keywords, additional_conditions=conditions)

            if price_data is not None and not price_data.empty:
                price_data['S_DQ_CLOSE'] = pd.to_numeric(price_data['S_DQ_CLOSE'], errors='coerce')
                price_data['S_DQ_AMOUNT'] = pd.to_numeric(price_data['S_DQ_AMOUNT'], errors='coerce') * 1000  # 千元转元

                self.save_data(price_data, 'stock_prices')
                print(f"获取到 {len(price_data)} 条正股价格数据")
                return price_data
            else:
                print("获取正股价格数据失败")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取正股价格数据出错: {e}")
            return pd.DataFrame()

    def _fetch_stock_market_cap_data(self, stock_codes: List[str]):
        """获取正股市值数据"""
        print("获取正股市值数据...")

        local_data = self.load_data('stock_market_cap')
        if not local_data.empty:
            return local_data

        try:
            keywords = ['S_INFO_WINDCODE', 'TRADE_DT', 'S_VAL_MV']
            conditions = {
                'S_INFO_WINDCODE': stock_codes,
                'TRADE_DT': [self.start_date.replace('-', ''), self.end_date.replace('-', '')]
            }

            market_cap_data = get_db_data('AShareEODDerivativeIndicator', keywords=keywords, additional_conditions=conditions)

            if market_cap_data is not None and not market_cap_data.empty:
                market_cap_data['S_VAL_MV'] = pd.to_numeric(market_cap_data['S_VAL_MV'], errors='coerce') / 10000  # 万元转亿元

                self.save_data(market_cap_data, 'stock_market_cap')
                print(f"获取到 {len(market_cap_data)} 条正股市值数据")
                return market_cap_data
            else:
                print("获取正股市值数据失败")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取正股市值数据出错: {e}")
            return pd.DataFrame()

    def _fetch_stock_financial_data(self, stock_codes: List[str]):
        """获取正股财务数据"""
        print("获取正股财务数据...")

        local_data = self.load_data('stock_financial')
        if not local_data.empty:
            return local_data

        try:
            keywords = ['S_INFO_WINDCODE', 'ANN_DT', 'NET_PROFIT_EXCL_MIN_INT_INC']
            conditions = {
                'S_INFO_WINDCODE': stock_codes,
                'ANN_DT': [self.start_date, self.end_date]
            }

            financial_data = get_db_data('AShareIncome', keywords=keywords, additional_conditions=conditions)

            if financial_data is not None and not financial_data.empty:
                financial_data['NET_PROFIT_EXCL_MIN_INT_INC'] = pd.to_numeric(
                    financial_data['NET_PROFIT_EXCL_MIN_INT_INC'], errors='coerce'
                )

                self.save_data(financial_data, 'stock_financial')
                print(f"获取到 {len(financial_data)} 条正股财务数据")
                return financial_data
            else:
                print("获取正股财务数据失败")
                return pd.DataFrame()

        except Exception as e:
            print(f"获取正股财务数据出错: {e}")
            return pd.DataFrame()

    def create_unified_dataset(self):
        """创建统一数据集"""
        print("=" * 80)
        print("开始创建统一数据集...")
        print("=" * 80)

        # 检查统一数据集是否已存在
        unified_file = self.get_data_file_path('unified_dataset')
        if unified_file.exists():
            print("发现已存在的统一数据集，正在加载...")
            self.unified_dataset = self.load_data('unified_dataset')
            if not self.unified_dataset.empty:
                print(f"统一数据集加载完成，共 {len(self.unified_dataset)} 条记录")
                return True

        # 加载所有数据
        print("加载所有原始数据...")
        bond_valuation = self.load_data('bond_valuation')
        bond_balance = self.load_data('bond_balance')
        bond_rating = self.load_data('bond_rating')
        bond_stock_mapping = self.load_data('bond_stock_mapping')
        stock_prices = self.load_data('stock_prices')
        stock_market_cap = self.load_data('stock_market_cap')
        stock_financial = self.load_data('stock_financial')
        bond_prices = self.load_data('bond_prices')
        trading_dates = self.load_data('trading_dates')

        # 检查必要数据是否存在
        required_data = [bond_valuation, bond_balance, bond_rating, bond_stock_mapping,
                        stock_prices, stock_market_cap, bond_prices, trading_dates]

        if any(df.empty for df in required_data):
            print("缺少必要的数据文件，请先运行 fetch_all_raw_data()")
            return False

        print("开始数据预处理和合并...")

        # 1. 预处理时间字段
        bond_valuation['TRADE_DT'] = pd.to_datetime(bond_valuation['TRADE_DT'])
        stock_prices['TRADE_DT'] = pd.to_datetime(stock_prices['TRADE_DT'])
        stock_market_cap['TRADE_DT'] = pd.to_datetime(stock_market_cap['TRADE_DT'])
        bond_prices['TRADE_DT'] = pd.to_datetime(bond_prices['TRADE_DT'])
        bond_balance['S_INFO_CHANGEDATE'] = pd.to_datetime(bond_balance['S_INFO_CHANGEDATE'])
        bond_rating['ANN_DT'] = pd.to_datetime(bond_rating['ANN_DT'])
        stock_financial['ANN_DT'] = pd.to_datetime(stock_financial['ANN_DT'])

        # 2. 获取所有交易日期
        all_dates = pd.to_datetime(trading_dates['TRADE_DT'])

        # 3. 创建基础数据框架（以可转债估值数据为基础）
        print("创建基础数据框架...")
        base_data = bond_valuation.copy()

        # 4. 合并可转债-正股映射关系
        print("合并可转债-正股映射关系...")
        base_data = base_data.merge(
            bond_stock_mapping[['S_INFO_WINDCODE', 'S_INFO_UNDERLYINGWINDCODE']],
            on='S_INFO_WINDCODE', how='left'
        )

        # 5. 合并可转债价格和成交额数据
        print("合并可转债价格和成交额数据...")
        base_data = base_data.merge(
            bond_prices[['S_INFO_WINDCODE', 'TRADE_DT', 'S_DQ_CLOSE', 'S_DQ_AMOUNT']].rename(
                columns={'S_DQ_CLOSE': 'BOND_PRICE', 'S_DQ_AMOUNT': 'BOND_AMOUNT'}
            ),
            on=['S_INFO_WINDCODE', 'TRADE_DT'], how='left'
        )

        # 6. 合并正股价格数据
        print("合并正股价格数据...")
        base_data = base_data.merge(
            stock_prices[['S_INFO_WINDCODE', 'TRADE_DT', 'S_DQ_CLOSE', 'S_DQ_AMOUNT']].rename(
                columns={'S_DQ_CLOSE': 'STOCK_PRICE', 'S_DQ_AMOUNT': 'STOCK_AMOUNT'}
            ),
            left_on=['S_INFO_UNDERLYINGWINDCODE', 'TRADE_DT'],
            right_on=['S_INFO_WINDCODE', 'TRADE_DT'], how='left',
            suffixes=('', '_stock')
        )

        # 7. 合并正股市值数据
        print("合并正股市值数据...")
        base_data = base_data.merge(
            stock_market_cap[['S_INFO_WINDCODE', 'TRADE_DT', 'S_VAL_MV']].rename(columns={'S_VAL_MV': 'STOCK_MARKET_CAP'}),
            left_on=['S_INFO_UNDERLYINGWINDCODE', 'TRADE_DT'],
            right_on=['S_INFO_WINDCODE', 'TRADE_DT'], how='left',
            suffixes=('', '_mv')
        )

        print("合并时间序列数据（余额、评级、财务）...")

        # 8. 使用向量化操作处理余额数据（完全避免apply）
        print("处理余额数据...")
        # 创建余额映射表
        bond_balance = bond_balance.rename(columns={'S_INFO_CHANGEDATE': 'TRADE_DT'})
        base_data = base_data.merge(bond_balance, on=['S_INFO_WINDCODE', 'TRADE_DT'], how='outer')
        base_data = base_data.sort_values(['S_INFO_WINDCODE', 'TRADE_DT'])
        base_data['B_INFO_OUTSTANDINGBALANCE'] = base_data.groupby('S_INFO_WINDCODE')['B_INFO_OUTSTANDINGBALANCE'].fillna(method='ffill')


        # 9. 使用向量化操作处理评级数据（完全避免apply）
        print("处理评级数据...")
        bond_rating = bond_rating.rename(columns={'ANN_DT': 'TRADE_DT'})
        base_data = base_data.merge(bond_rating, on=['S_INFO_WINDCODE', 'TRADE_DT'], how='outer')
        base_data = base_data.sort_values(['S_INFO_WINDCODE', 'TRADE_DT'])
        base_data['B_INFO_CREDITRATING'] = base_data.groupby('S_INFO_WINDCODE')['B_INFO_CREDITRATING'].fillna(method='ffill')

        stock_financial = stock_financial.rename(columns={'ANN_DT': 'TRADE_DT','S_INFO_WINDCODE':'S_INFO_UNDERLYINGWINDCODE'})
        base_data = base_data.merge(stock_financial, on=['S_INFO_UNDERLYINGWINDCODE', 'TRADE_DT'], how='outer')
        base_data = base_data.sort_values(['S_INFO_WINDCODE','S_INFO_UNDERLYINGWINDCODE', 'TRADE_DT'])
        base_data['NET_PROFIT_EXCL_MIN_INT_INC'] = base_data.groupby('S_INFO_UNDERLYINGWINDCODE')['NET_PROFIT_EXCL_MIN_INT_INC'].fillna(method='ffill')


        # 11. 清理和重命名列
        print("清理和重命名列...")
        final_columns = {
            'S_INFO_WINDCODE': 'BOND_CODE',
            'TRADE_DT': 'TRADE_DATE',
            'CB_ANAL_YTM': 'YTM',
            'CB_ANAL_PTM': 'PTM',
            'CB_ANAL_CONVPREMIUMRATIO': 'CONV_PREMIUM_RATIO',
            'S_INFO_UNDERLYINGWINDCODE': 'STOCK_CODE',
            'BOND_PRICE': 'BOND_PRICE',
            'BOND_AMOUNT': 'BOND_AMOUNT',
            'STOCK_PRICE': 'STOCK_PRICE',
            'STOCK_AMOUNT': 'STOCK_AMOUNT',
            'STOCK_MARKET_CAP': 'STOCK_MARKET_CAP',
            'B_INFO_OUTSTANDINGBALANCE': 'BOND_BALANCE',
            'B_INFO_CREDITRATING': 'BOND_RATING',
            'NET_PROFIT_EXCL_MIN_INT_INC': 'STOCK_NET_PROFIT'
        }

        # 选择需要的列并重命名
        available_columns = [col for col in final_columns.keys() if col in base_data.columns]
        base_data = base_data[available_columns].rename(columns=final_columns)

        base_data = base_data.sort_values(['BOND_CODE','STOCK_CODE', 'TRADE_DATE'])
        base_data['BOND_PRICE'] = base_data.groupby('BOND_CODE')['BOND_PRICE'].fillna(method='ffill')
        base_data['BOND_AMOUNT'] = base_data.groupby('BOND_CODE')['BOND_AMOUNT'].fillna(method='ffill')
        base_data['STOCK_PRICE'] = base_data.groupby('STOCK_CODE')['STOCK_PRICE'].fillna(method='ffill')
        base_data['STOCK_AMOUNT'] = base_data.groupby('STOCK_CODE')['STOCK_AMOUNT'].fillna(method='ffill')
        base_data['STOCK_MARKET_CAP'] = base_data.groupby('STOCK_CODE')['STOCK_MARKET_CAP'].fillna(method='ffill')
        base_data['BOND_RATING'] = base_data.groupby('BOND_CODE')['BOND_RATING'].fillna(method='ffill')
        base_data['STOCK_NET_PROFIT'] = base_data.groupby('STOCK_CODE')['STOCK_NET_PROFIT'].fillna(method='ffill')
        base_data['BOND_BALANCE'] = base_data.groupby('BOND_CODE')['BOND_BALANCE'].fillna(method='ffill')
        base_data['BOND_RATING'] = base_data.groupby('BOND_CODE')['BOND_RATING'].fillna(method='ffill')
        base_data['YTM'] = base_data.groupby('BOND_CODE')['YTM'].fillna(method='ffill')
        base_data['PTM'] = base_data.groupby('BOND_CODE')['PTM'].fillna(method='ffill')
        base_data['CONV_PREMIUM_RATIO'] = base_data.groupby('BOND_CODE')['CONV_PREMIUM_RATIO'].fillna(method='ffill')
        base_data = base_data.dropna(subset=['BOND_CODE','STOCK_CODE','TRADE_DATE'])

        self.unified_dataset = base_data
        
        # 12. 数据类型优化和缺失值处理
        print("数据类型优化和缺失值处理...")
        numeric_columns = ['YTM', 'PTM', 'CONV_PREMIUM_RATIO', 'BOND_PRICE', 'BOND_AMOUNT', 'STOCK_PRICE',
                          'STOCK_AMOUNT', 'STOCK_MARKET_CAP', 'BOND_BALANCE', 'STOCK_NET_PROFIT']

        for col in numeric_columns:
            if col in self.unified_dataset.columns:
                self.unified_dataset[col] = pd.to_numeric(self.unified_dataset[col], errors='coerce')

        # 13. 保存统一数据集
        print("保存统一数据集...")
        self.save_data(self.unified_dataset, 'unified_dataset')

        print("=" * 80)
        print(f"统一数据集创建完成！共 {len(self.unified_dataset)} 条记录")
        print(f"数据字段: {list(self.unified_dataset.columns)}")
        print(f"数据时间范围: {self.unified_dataset['TRADE_DATE'].min()} 至 {self.unified_dataset['TRADE_DATE'].max()}")
        print("=" * 80)

        return True


class UnifiedScreener:
    """统一筛选器 - 在完整数据集基础上进行优化筛选"""

    def __init__(self):
        # 筛选条件
        self.min_stock_price = 4.0          # 正股收盘价 >= 4元
        self.min_bond_balance = 5.0         # 转债余额 >= 5亿元
        self.min_market_cap = 40.0          # 正股市值 >= 40亿元
        self.min_remaining_term = 1.0       # 转债剩余期限 >= 1年
        self.min_rating = 'A+'              # 转债评级 >= A+
        self.ytm_percentile = 0.8           # YTM前20%
        self.exclude_loss_stocks = True     # 排除亏损股票
        self.min_avg_amount = 10000000      # 20日均成交额 >= 1000万元

        # 评级排序
        self.rating_order = ['AAA', 'AA+', 'AA', 'AA-', 'A+', 'A', 'A-', 'BBB+', 'BBB', 'BBB-']
        self.min_rating_index = self.rating_order.index(self.min_rating) if self.min_rating in self.rating_order else len(self.rating_order)

    def screen_bonds_for_date(self, unified_data: pd.DataFrame, date: str) -> pd.DataFrame:
        """对指定日期进行可转债筛选（基于统一数据集）"""
        print(f"\n=== 筛选日期: {date} ===")

        # 筛选指定日期的数据
        date_data = unified_data[unified_data['TRADE_DATE'] == date].copy()

        if date_data.empty:
            print(f"没有找到 {date} 的数据")
            return pd.DataFrame()

        original_count = len(date_data)
        print(f"开始筛选，原始数据: {original_count} 条")

        # 1. 正股收盘价 >= 4元
        date_data = date_data[date_data['STOCK_PRICE'] >= self.min_stock_price]
        print(f"正股价格筛选后: {len(date_data)} 条")

        # 2. 转债余额 >= 5亿元
        date_data = date_data[date_data['BOND_BALANCE'] >= self.min_bond_balance]
        print(f"转债余额筛选后: {len(date_data)} 条")

        # 3. 正股市值 >= 40亿元
        date_data = date_data[date_data['STOCK_MARKET_CAP'] >= self.min_market_cap]
        print(f"正股市值筛选后: {len(date_data)} 条")

        # 4. 排除亏损股票
        if self.exclude_loss_stocks:
            profit_condition = (date_data['STOCK_NET_PROFIT'].fillna(1) > 0)
            date_data = date_data[profit_condition]
            print(f"排除亏损股票后: {len(date_data)} 条")

        # 5. 转债评级 >= A+
        def is_rating_qualified(rating):
            if pd.isna(rating) or rating == '':
                return False
            try:
                return self.rating_order.index(rating) <= self.min_rating_index
            except ValueError:
                return False

        date_data = date_data[date_data['BOND_RATING'].apply(is_rating_qualified)]
        print(f"评级筛选后: {len(date_data)} 条")

        # 6. YTM前20%
        if 'YTM' in date_data.columns:
            date_data = date_data.dropna(subset=['YTM'])
            if not date_data.empty:
                ytm_threshold = date_data['YTM'].quantile(self.ytm_percentile)
                date_data = date_data[date_data['YTM'] >= ytm_threshold]
                print(f"YTM筛选后: {len(date_data)} 条")
            else:
                print("YTM筛选后: 0 条 (无有效YTM数据)")
        else:
            print("警告: YTM字段不存在，跳过YTM筛选")

        # 7. 20日均成交额筛选（这里简化处理，使用当日成交额）
        date_data = date_data[date_data['STOCK_AMOUNT'] >= self.min_avg_amount]
        print(f"成交额筛选后: {len(date_data)} 条")

        print(f"筛选完成，从 {original_count} 条筛选出 {len(date_data)} 条")

        # 返回筛选结果
        if not date_data.empty:
            return date_data[['BOND_CODE', 'YTM']].rename(columns={'BOND_CODE': 'bond_code'})
        else:
            return pd.DataFrame()

    def batch_screen_bonds(self, unified_data: pd.DataFrame, dates: List[str]) -> Dict[str, List[str]]:
        """批量筛选多个日期的可转债"""
        print("=" * 80)
        print("开始批量筛选可转债...")
        print("=" * 80)

        all_selections = {}

        for date in dates:
            selected_bonds = self.screen_bonds_for_date(unified_data, date)
            if not selected_bonds.empty:
                all_selections[date] = selected_bonds['bond_code'].tolist()
            else:
                all_selections[date] = []

        print("=" * 80)
        print("批量筛选完成！")
        print("=" * 80)

        return all_selections


class ConvertibleBondStrategyUnified:
    """可转债量化策略 - 统一数据处理版本"""

    def __init__(self, start_date: str = '2020-01-01', end_date: str = '2025-06-30'):
        """
        初始化策略

        参数:
        start_date: 策略开始日期
        end_date: 策略结束日期
        """
        self.start_date = start_date
        self.end_date = end_date
        print(f"策略期间: {start_date} 至 {end_date}")

        # 创建数据存储目录
        self.data_folder = Path('BatchData')
        self.data_folder.mkdir(exist_ok=True)

        # 初始化各模块
        self.data_processor = UnifiedDataProcessor(start_date, end_date, self.data_folder)
        self.screener = UnifiedScreener()

        print("可转债量化策略（统一数据处理版本）初始化完成")
        print(f"策略期间: {start_date} 至 {end_date}")
        print("筛选条件:")
        print(f"  - 正股收盘价 ≥ {self.screener.min_stock_price}元")
        print(f"  - 转债余额 ≥ {self.screener.min_bond_balance}亿元")
        print(f"  - 正股市值 ≥ {self.screener.min_market_cap}亿元")
        print(f"  - 转债剩余期限 ≥ {self.screener.min_remaining_term}年")
        print(f"  - 转债评级 ≥ {self.screener.min_rating}")
        print(f"  - YTM排名前{int((1-self.screener.ytm_percentile)*100)}%")
        print(f"  - 排除亏损股票" if self.screener.exclude_loss_stocks else "  - 包含亏损股票")
        print(f"  - 20日均成交额 ≥ {self.screener.min_avg_amount/10000}万元")

    def prepare_data(self):
        """准备数据（获取原始数据并创建统一数据集）"""
        print("=" * 80)
        print("开始数据准备流程...")
        print("=" * 80)

        # 1. 获取原始数据
        success = self.data_processor.fetch_all_raw_data()
        if not success:
            print("原始数据获取失败")
            return False

        # 2. 创建统一数据集
        success = self.data_processor.create_unified_dataset()
        if not success:
            print("统一数据集创建失败")
            return False

        print("=" * 80)
        print("数据准备完成！")
        print("=" * 80)
        return True

    def get_month_end_dates(self, trading_dates: pd.DataFrame) -> List[str]:
        """获取月末交易日"""
        try:
            dates = pd.to_datetime(trading_dates['TRADE_DT'])
            month_ends = []

            start_dt = pd.to_datetime(self.start_date)
            end_dt = pd.to_datetime(self.end_date)

            for year in range(start_dt.year, end_dt.year + 1):
                for month in range(1, 13):
                    month_dates = dates[
                        (dates.dt.year == year) & (dates.dt.month == month)
                    ]

                    if not month_dates.empty:
                        last_date_dt = month_dates.max()
                        if start_dt <= last_date_dt <= end_dt:
                            last_date = last_date_dt.strftime('%Y%m%d')
                            month_ends.append(last_date)

            return sorted(month_ends)

        except Exception as e:
            print(f"获取月末交易日出错: {e}")
            return []

    def run_strategy(self):
        """运行策略"""
        print("=" * 80)
        print("开始运行统一数据处理版本的可转债策略...")
        print("=" * 80)

        # 1. 检查统一数据集是否存在
        if self.data_processor.unified_dataset.empty:
            print("统一数据集不存在，尝试加载...")
            unified_file = self.data_processor.get_data_file_path('unified_dataset')
            if unified_file.exists():
                self.data_processor.unified_dataset = self.data_processor.load_data('unified_dataset')
            else:
                print("请先运行 prepare_data() 准备数据")
                return pd.DataFrame(), pd.DataFrame()

        # 2. 加载交易日历
        trading_dates = self.data_processor.load_data('trading_dates')
        if trading_dates.empty:
            print("请先运行 prepare_data() 获取数据")
            return pd.DataFrame(), pd.DataFrame()

        # 3. 获取月末交易日
        month_end_dates = self.get_month_end_dates(trading_dates)
        print(f"获取到 {len(month_end_dates)} 个月末交易日")

        if not month_end_dates:
            print("没有找到月末交易日")
            return pd.DataFrame(), pd.DataFrame()

        # 4. 批量筛选所有月末日期
        all_selections = self.screener.batch_screen_bonds(
            self.data_processor.unified_dataset,
            month_end_dates
        )

        # 5. 构建持仓矩阵
        position_data = self.build_position_matrix(all_selections, trading_dates)

        # 6. 获取价格数据
        price_data = self.build_price_matrix(position_data)

        return price_data, position_data

    def build_position_matrix(self, selections: Dict[str, List[str]], trading_dates: pd.DataFrame) -> pd.DataFrame:
        """构建持仓矩阵"""
        print("\n构建持仓矩阵...")

        # 获取所有选中的债券
        all_bonds = set()
        for bonds in selections.values():
            all_bonds.update(bonds)
        all_bonds = sorted(list(all_bonds))

        if not all_bonds:
            print("没有选中任何债券")
            return pd.DataFrame()

        print(f"总共涉及 {len(all_bonds)} 只可转债")

        # 创建持仓矩阵
        dates = pd.to_datetime(trading_dates['TRADE_DT'])
        position_matrix = pd.DataFrame(0.0, index=dates, columns=all_bonds)

        # 填充持仓权重
        for rebalance_date, selected_bonds in selections.items():
            if selected_bonds:
                # 等权重分配
                weight = 1.0 / len(selected_bonds)

                # 找到调仓日期之后的所有交易日，直到下一个调仓日
                rebalance_dt = pd.to_datetime(rebalance_date)

                # 找到下一个调仓日
                next_rebalance_dates = [d for d in selections.keys() if d > rebalance_date]
                if next_rebalance_dates:
                    next_rebalance_dt = pd.to_datetime(min(next_rebalance_dates))
                    mask = (position_matrix.index >= rebalance_dt) & (position_matrix.index < next_rebalance_dt)
                else:
                    mask = position_matrix.index >= rebalance_dt

                # 设置持仓权重
                for bond in selected_bonds:
                    if bond in position_matrix.columns:
                        position_matrix.loc[mask, bond] = weight

        print(f"持仓矩阵构建完成，形状: {position_matrix.shape}")
        return position_matrix

    def build_price_matrix(self, position_data: pd.DataFrame) -> pd.DataFrame:
        """构建价格矩阵（基于统一数据集）"""
        print("\n构建价格矩阵...")

        if position_data.empty:
            return pd.DataFrame()

        # 从统一数据集中获取价格数据
        unified_data = self.data_processor.unified_dataset
        if unified_data.empty:
            print("统一数据集为空")
            return pd.DataFrame()

        # 获取需要的债券和日期
        needed_bonds = position_data.columns.tolist()
        needed_dates = position_data.index.strftime('%Y%m%d').tolist()

        # 筛选相关的价格数据
        relevant_prices = unified_data[
            (unified_data['BOND_CODE'].isin(needed_bonds)) &
            (unified_data['TRADE_DATE'].dt.strftime('%Y%m%d').isin(needed_dates))
        ].copy()

        print(f"获取到 {len(relevant_prices)} 条价格数据")

        if relevant_prices.empty:
            print("没有找到相关的价格数据")
            return pd.DataFrame()

        # 构建价格矩阵
        price_matrix = relevant_prices.pivot(
            index='TRADE_DATE',
            columns='BOND_CODE',
            values='BOND_PRICE'
        )

        # 重新索引以匹配持仓矩阵
        price_matrix = price_matrix.reindex(index=position_data.index, columns=position_data.columns)

        # 前向填充缺失价格
        price_matrix = price_matrix.fillna(method='ffill')

        print(f"价格矩阵构建完成，形状: {price_matrix.shape}")
        print(f"缺失数据点: {price_matrix.isna().sum().sum()}")

        return price_matrix

    def calculate_strategy_returns(self, price_data: pd.DataFrame, position_data: pd.DataFrame) -> pd.DataFrame:
        """计算策略收益率"""
        print("\n计算策略收益率...")

        if price_data.empty or position_data.empty:
            return pd.DataFrame()

        # 计算日收益率
        returns = price_data.pct_change().fillna(0)

        # 计算策略日收益率
        strategy_returns = (returns * position_data.shift(1)).sum(axis=1)
        strategy_returns.iloc[0] = 0  # 第一天收益率为0

        # 计算累计收益率和净值
        cumulative_returns = (1 + strategy_returns).cumprod()

        # 构建结果DataFrame
        results_df = pd.DataFrame({
            'date': strategy_returns.index,
            'daily_return': strategy_returns.values,
            'cumulative_return': cumulative_returns.values,
            'net_value': cumulative_returns.values
        })

        print(f"策略收益率计算完成，数据点: {len(results_df)}")
        return results_df

    def save_results(self, returns_df: pd.DataFrame, position_data: pd.DataFrame) -> Tuple[Path, Path]:
        """保存结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存收益率数据
        returns_file = Path('Results') / f'统一数据转债策略收益率_{timestamp}.xlsx'
        returns_file.parent.mkdir(exist_ok=True)
        returns_df.to_excel(returns_file, index=False)

        # 保存持仓数据
        position_file = Path('Results') / f'统一数据转债策略持仓_{timestamp}.xlsx'
        position_data.to_excel(position_file)

        print(f"\n结果已保存:")
        print(f"收益率数据: {returns_file}")
        print(f"持仓数据: {position_file}")

        return returns_file, position_file

    def run_backtest(self):
        """运行完整的回测流程"""
        print("=" * 80)
        print("开始运行统一数据处理版本的可转债策略回测")
        print("=" * 80)

        try:
            # 1. 检查是否已有统一数据集，如果没有则准备数据
            unified_file = self.data_processor.get_data_file_path('unified_dataset')
            if not unified_file.exists():
                print("统一数据集不存在，开始准备数据...")
                success = self.prepare_data()
                if not success:
                    print("数据准备失败，无法进行回测")
                    return
            else:
                print("使用已有的统一数据集进行回测...")
                # 加载统一数据集
                self.data_processor.unified_dataset = self.data_processor.load_data('unified_dataset')

            # 2. 运行策略
            price_data, position_data = self.run_strategy()

            if price_data.empty or position_data.empty:
                print("策略运行失败，无法进行回测")
                return

            # 3. 计算策略收益率
            returns_df = self.calculate_strategy_returns(price_data, position_data)

            # 4. 保存结果
            self.save_results(returns_df, position_data)

            # 5. 显示基本统计
            if not returns_df.empty:
                final_nav = returns_df['net_value'].iloc[-1]
                total_return = (final_nav - 1) * 100

                print(f"\n=== 策略表现 ===")
                print(f"回测期间: {self.start_date} 至 {self.end_date}")
                print(f"最终净值: {final_nav:.4f}")
                print(f"累计收益率: {total_return:.2f}%")

                if len(returns_df) > 1:
                    daily_returns = returns_df['daily_return'].dropna()
                    if len(daily_returns) > 0:
                        annual_return = daily_returns.mean() * 252 * 100
                        volatility = daily_returns.std() * np.sqrt(252) * 100
                        sharpe = annual_return / volatility if volatility > 0 else 0

                        print(f"年化收益率: {annual_return:.2f}%")
                        print(f"年化波动率: {volatility:.2f}%")
                        print(f"夏普比率: {sharpe:.2f}")

            print("\n统一数据处理版本策略回测完成！")

        except Exception as e:
            print(f"回测过程中出现错误: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主程序"""
    print("可转债量化策略 - 统一数据处理版本")
    print("=" * 60)

    while True:
        print("\n请选择操作:")
        print("1. 准备数据（获取原始数据并创建统一数据集）")
        print("2. 运行短期回测（2024年1-3月）")
        print("3. 运行中期回测（2024年全年）")
        print("4. 运行长期回测（2020-2024年）")
        print("5. 自定义时间段回测")
        print("6. 查看统一数据集状态")
        print("7. 退出")

        choice = input("\n请输入选择 (1-7): ").strip()

        if choice == '1':
            print("\n" + "=" * 60)
            print("开始准备数据...")
            start_date = input("请输入开始日期 (YYYY-MM-DD, 默认2020-01-01): ").strip() or '2020-01-01'
            end_date = input("请输入结束日期 (YYYY-MM-DD, 默认2024-12-31): ").strip() or '2024-12-31'

            strategy = ConvertibleBondStrategyUnified(start_date, end_date)
            strategy.prepare_data()

        elif choice == '2':
            print("\n" + "=" * 60)
            print("运行短期回测（2024年1-3月）")
            strategy = ConvertibleBondStrategyUnified('2024-01-01', '2024-03-31')
            strategy.run_backtest()

        elif choice == '3':
            print("\n" + "=" * 60)
            print("运行中期回测（2024年全年）")
            strategy = ConvertibleBondStrategyUnified('2024-01-01', '2024-12-31')
            strategy.run_backtest()

        elif choice == '4':
            print("\n" + "=" * 60)
            print("运行长期回测（2020-2024年）")
            strategy = ConvertibleBondStrategyUnified('2020-01-01', '2024-12-31')
            strategy.run_backtest()

        elif choice == '5':
            print("\n" + "=" * 60)
            print("自定义时间段回测")
            start_date = input("请输入开始日期 (YYYY-MM-DD): ").strip()
            end_date = input("请输入结束日期 (YYYY-MM-DD): ").strip()

            if start_date and end_date:
                strategy = ConvertibleBondStrategyUnified(start_date, end_date)
                strategy.run_backtest()
            else:
                print("日期输入无效")

        elif choice == '6':
            print("\n" + "=" * 60)
            print("统一数据集状态:")
            data_folder = Path('BatchData')
            if data_folder.exists():
                unified_files = list(data_folder.glob('unified_dataset_*.pkl'))
                if unified_files:
                    print(f"找到 {len(unified_files)} 个统一数据集文件:")
                    for file in sorted(unified_files):
                        size_mb = file.stat().st_size / (1024 * 1024)
                        print(f"  - {file.name} ({size_mb:.1f} MB)")
                else:
                    print("没有找到统一数据集文件")

                # 显示所有数据文件
                all_files = list(data_folder.glob('*.pkl'))
                if all_files:
                    print(f"\n所有数据文件 ({len(all_files)} 个):")
                    for file in sorted(all_files):
                        size_mb = file.stat().st_size / (1024 * 1024)
                        print(f"  - {file.name} ({size_mb:.1f} MB)")
            else:
                print("数据目录不存在")

        elif choice == '7':
            print("退出程序")
            break

        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
