#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试转债成交额功能
"""

from convertible_bond_strategy_unified import ConvertibleBondStrategyUnified, UnifiedDataProcessor
import pandas as pd
import numpy as np
from pathlib import Path

def create_test_data_with_bond_amount():
    """创建包含转债成交额的测试数据"""
    print("创建包含转债成交额的测试数据...")
    
    # 创建交易日期
    dates = pd.date_range('2024-01-01', '2024-01-10', freq='D')
    trading_dates = dates[dates.weekday < 5]  # 只保留工作日
    
    # 创建可转债和正股代码
    bond_codes = [f'bond_{i:03d}' for i in range(1, 6)]  # 5只可转债
    stock_codes = [f'stock_{i:03d}' for i in range(1, 6)]  # 5只正股
    
    # 创建模拟数据
    data_folder = Path('TestBondAmount')
    data_folder.mkdir(exist_ok=True)
    
    processor = UnifiedDataProcessor('2024-01-01', '2024-01-10', data_folder)
    
    # 1. 交易日历
    trading_dates_df = pd.DataFrame({'TRADE_DT': trading_dates.strftime('%Y%m%d')})
    processor.save_data(trading_dates_df, 'trading_dates')
    
    # 2. 可转债估值数据
    bond_valuation_data = []
    for date in trading_dates:
        date_str = date.strftime('%Y%m%d')
        for bond_code in bond_codes:
            bond_valuation_data.append({
                'S_INFO_WINDCODE': bond_code,
                'TRADE_DT': date_str,
                'CB_ANAL_YTM': np.random.uniform(0.01, 0.08),
                'CB_ANAL_PTM': np.random.uniform(0.5, 3.0),
                'CB_ANAL_CONVPREMIUMRATIO': np.random.uniform(0.05, 0.30)
            })
    
    bond_valuation_df = pd.DataFrame(bond_valuation_data)
    processor.save_data(bond_valuation_df, 'bond_valuation')
    
    # 3. 可转债余额数据
    bond_balance_data = []
    for bond_code in bond_codes:
        bond_balance_data.append({
            'S_INFO_WINDCODE': bond_code,
            'S_INFO_CHANGEDATE': '20240101',
            'B_INFO_OUTSTANDINGBALANCE': np.random.uniform(5, 20)
        })
    
    bond_balance_df = pd.DataFrame(bond_balance_data)
    processor.save_data(bond_balance_df, 'bond_balance')
    
    # 4. 可转债评级数据
    ratings = ['AAA', 'AA+', 'AA', 'AA-', 'A+']
    bond_rating_data = []
    for bond_code in bond_codes:
        bond_rating_data.append({
            'S_INFO_WINDCODE': bond_code,
            'ANN_DT': '20240101',
            'B_INFO_CREDITRATING': np.random.choice(ratings)
        })
    
    bond_rating_df = pd.DataFrame(bond_rating_data)
    processor.save_data(bond_rating_df, 'bond_rating')
    
    # 5. 可转债-正股映射
    mapping_data = pd.DataFrame({
        'S_INFO_WINDCODE': bond_codes,
        'S_INFO_UNDERLYINGWINDCODE': stock_codes
    })
    processor.save_data(mapping_data, 'bond_stock_mapping')
    
    # 6. 正股价格数据
    stock_price_data = []
    for date in trading_dates:
        date_str = date.strftime('%Y%m%d')
        for stock_code in stock_codes:
            stock_price_data.append({
                'S_INFO_WINDCODE': stock_code,
                'TRADE_DT': date_str,
                'S_DQ_CLOSE': np.random.uniform(10, 50),
                'S_DQ_AMOUNT': np.random.uniform(5000000, 50000000)
            })
    
    stock_price_df = pd.DataFrame(stock_price_data)
    processor.save_data(stock_price_df, 'stock_prices')
    
    # 7. 正股市值数据
    stock_market_cap_data = []
    for date in trading_dates:
        date_str = date.strftime('%Y%m%d')
        for stock_code in stock_codes:
            stock_market_cap_data.append({
                'S_INFO_WINDCODE': stock_code,
                'TRADE_DT': date_str,
                'S_VAL_MV': np.random.uniform(50, 200)
            })
    
    stock_market_cap_df = pd.DataFrame(stock_market_cap_data)
    processor.save_data(stock_market_cap_df, 'stock_market_cap')
    
    # 8. 正股财务数据
    stock_financial_data = []
    for stock_code in stock_codes:
        stock_financial_data.append({
            'S_INFO_WINDCODE': stock_code,
            'ANN_DT': '20240101',
            'NET_PROFIT_EXCL_MIN_INT_INC': np.random.uniform(1000000, 5000000)
        })
    
    stock_financial_df = pd.DataFrame(stock_financial_data)
    processor.save_data(stock_financial_df, 'stock_financial')
    
    # 9. 可转债价格和成交额数据（重点测试）
    bond_price_data = []
    for date in trading_dates:
        date_str = date.strftime('%Y%m%d')
        for bond_code in bond_codes:
            bond_price_data.append({
                'S_INFO_WINDCODE': bond_code,
                'TRADE_DT': date_str,
                'S_DQ_CLOSE': np.random.uniform(100, 150),
                'S_DQ_AMOUNT': np.random.uniform(1000, 10000)  # 千元为单位
            })
    
    bond_price_df = pd.DataFrame(bond_price_data)
    processor.save_data(bond_price_df, 'bond_prices')
    
    print(f"测试数据创建完成:")
    print(f"  - 交易日期: {len(trading_dates)} 天")
    print(f"  - 可转债数量: {len(bond_codes)} 只")
    print(f"  - 转债价格数据: {len(bond_price_df)} 条（包含成交额）")
    
    return processor

def test_bond_amount_functionality():
    """测试转债成交额功能"""
    print("=" * 60)
    print("测试转债成交额功能")
    print("=" * 60)
    
    # 创建测试数据
    processor = create_test_data_with_bond_amount()
    
    # 测试统一数据集创建
    print("\n开始测试统一数据集创建（包含转债成交额）...")
    success = processor.create_unified_dataset()
    
    if success:
        print(f"✅ 统一数据集创建成功")
        print(f"📊 数据集大小: {len(processor.unified_dataset)} 条记录")
        print(f"📋 数据字段: {list(processor.unified_dataset.columns)}")
        
        # 检查是否包含转债成交额字段
        if 'BOND_AMOUNT' in processor.unified_dataset.columns:
            print("✅ 转债成交额字段 BOND_AMOUNT 已成功添加")
            
            # 检查数据质量
            bond_amount_data = processor.unified_dataset['BOND_AMOUNT']
            print(f"📈 转债成交额数据统计:")
            print(f"  - 有效数据: {bond_amount_data.notna().sum()} 条")
            print(f"  - 缺失数据: {bond_amount_data.isna().sum()} 条")
            print(f"  - 数据范围: {bond_amount_data.min():.0f} - {bond_amount_data.max():.0f} 元")
            print(f"  - 平均值: {bond_amount_data.mean():.0f} 元")
            
            # 显示样本数据
            print(f"\n📋 转债成交额样本数据:")
            sample_data = processor.unified_dataset[['BOND_CODE', 'TRADE_DATE', 'BOND_PRICE', 'BOND_AMOUNT']].head(10)
            for _, row in sample_data.iterrows():
                print(f"  {row['BOND_CODE']} | {row['TRADE_DATE'].strftime('%Y-%m-%d')} | 价格: {row['BOND_PRICE']:.2f} | 成交额: {row['BOND_AMOUNT']:.0f}元")
            
            return True
        else:
            print("❌ 转债成交额字段 BOND_AMOUNT 未找到")
            return False
    else:
        print("❌ 统一数据集创建失败")
        return False

def test_strategy_with_bond_amount():
    """测试包含转债成交额的策略功能"""
    print("\n" + "=" * 60)
    print("测试包含转债成交额的策略功能")
    print("=" * 60)
    
    # 使用包含转债成交额的数据创建策略
    strategy = ConvertibleBondStrategyUnified('2024-01-01', '2024-01-10')
    strategy.data_processor.data_folder = Path('TestBondAmount')
    
    # 加载统一数据集
    unified_file = strategy.data_processor.get_data_file_path('unified_dataset')
    if unified_file.exists():
        strategy.data_processor.unified_dataset = strategy.data_processor.load_data('unified_dataset')
        
        print(f"✅ 策略数据加载成功")
        print(f"📊 数据集大小: {len(strategy.data_processor.unified_dataset)} 条记录")
        
        # 检查转债成交额字段
        if 'BOND_AMOUNT' in strategy.data_processor.unified_dataset.columns:
            print("✅ 策略中包含转债成交额字段")
            
            # 测试筛选功能
            print("\n测试筛选功能...")
            test_date = '20240102'
            screener_result = strategy.screener.screen_bonds_for_date(
                strategy.data_processor.unified_dataset, 
                test_date
            )
            
            if not screener_result.empty:
                print(f"✅ 筛选功能正常，筛选出 {len(screener_result)} 只转债")
            else:
                print("⚠️ 筛选结果为空（可能是筛选条件过严）")
            
            return True
        else:
            print("❌ 策略中未找到转债成交额字段")
            return False
    else:
        print("❌ 统一数据集文件不存在")
        return False

def main():
    """主测试函数"""
    try:
        # 测试转债成交额功能
        success1 = test_bond_amount_functionality()
        
        if success1:
            # 测试策略功能
            success2 = test_strategy_with_bond_amount()
            
            if success1 and success2:
                print("\n" + "=" * 80)
                print("🎉 转债成交额功能测试全部通过！")
                print("✅ 转债成交额数据获取功能正常")
                print("✅ 统一数据集包含转债成交额字段")
                print("✅ 策略筛选功能兼容转债成交额")
                print("✅ 数据类型转换正确（千元转元）")
                print("=" * 80)
            else:
                print("\n❌ 部分测试失败")
        else:
            print("\n❌ 转债成交额功能测试失败")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
